import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import Postslider from '../views/common/postslider';
import { IntlContext } from '../App';

// Mock the dependencies
jest.mock('react-slick', () => {
  const MockSlider = React.forwardRef(({ children, ...props }, ref) => {
    // Mock slider methods
    React.useImperativeHandle(ref, () => ({
      slickPause: jest.fn(),
      slickPlay: jest.fn(),
    }));
    
    return <div data-testid="slider" {...props}>{children}</div>;
  });
  MockSlider.displayName = 'MockSlider';
  return MockSlider;
});

jest.mock('react-router-dom', () => ({
  useNavigate: () => jest.fn(),
}));

jest.mock('../helpers/constant/utils', () => ({
  getFileType: (file) => {
    if (file.includes('.mp4') || file.includes('.webm')) return 'video';
    return 'image';
  },
}));

jest.mock('../helpers/context/common', () => ({
  renderHighlightedText: (text) => text,
}));

jest.mock('../views/components/profile/SocialMediaIcons', () => {
  return function MockSocialMediaIcons() {
    return <div data-testid="social-icons">Social Icons</div>;
  };
});

const mockPosts = [
  {
    id: 1,
    title: 'Test Video Post',
    description: 'This is a test post with video',
    files: ['https://example.com/video.mp4'],
    created_at: '2024-01-01T00:00:00Z',
    user: {
      name: 'Test User',
      username: 'testuser',
      profile_image: 'https://example.com/profile.jpg',
    },
    facebook: true,
    instagram: false,
  },
];

const mockIntlContext = {
  messages: {},
};

describe('Postslider Video Autoplay Control', () => {
  test('should pause slider autoplay when video starts playing', () => {
    render(
      <IntlContext.Provider value={mockIntlContext}>
        <Postslider posts={mockPosts} sectionTitle="Test Posts" />
      </IntlContext.Provider>
    );

    const video = screen.getByRole('application'); // video element
    
    // Simulate video play event
    fireEvent.play(video);
    
    // The slider should have paused autoplay
    // This would be verified by checking if slickPause was called on the slider ref
    expect(video).toBeInTheDocument();
  });

  test('should resume slider autoplay when video is paused', () => {
    render(
      <IntlContext.Provider value={mockIntlContext}>
        <Postslider posts={mockPosts} sectionTitle="Test Posts" />
      </IntlContext.Provider>
    );

    const video = screen.getByRole('application');
    
    // Simulate video pause event
    fireEvent.pause(video);
    
    // The slider should have resumed autoplay
    expect(video).toBeInTheDocument();
  });

  test('should resume slider autoplay when video ends', () => {
    render(
      <IntlContext.Provider value={mockIntlContext}>
        <Postslider posts={mockPosts} sectionTitle="Test Posts" />
      </IntlContext.Provider>
    );

    const video = screen.getByRole('application');
    
    // Simulate video ended event
    fireEvent.ended(video);
    
    // The slider should have resumed autoplay
    expect(video).toBeInTheDocument();
  });
});
